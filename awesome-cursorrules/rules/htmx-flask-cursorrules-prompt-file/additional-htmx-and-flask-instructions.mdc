---
description: Provides additional instructions for HTMX and Flask, primarily related to templating.
globs: templates/**/*.*
---
- Use Jinja2 templating with HTMX attributes
- Implement proper CSRF protection with Flask-WTF
- Utilize Flask's request object for handling HTMX requests
- Use Flask-Migrate for database migrations
- Implement proper error handling and logging
- Follow Flask's application factory pattern
- Use environment variables for configuration