---
description: Applies best practices for HTMX and Flask development within the app directory.
globs: app/**/*.*
---
- Use Flask's render_template for server-side rendering
- Implement Flask-WTF for form handling
- Utilize Flask's url_for for generating URLs
- Use Flask's jsonify for JSON responses
- Implement Flask-SQLAlchemy for database operations
- Utilize Flask's Blueprint for modular applications