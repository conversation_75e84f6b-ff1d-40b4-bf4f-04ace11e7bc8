// HTMX with Django .cursorrules

// HTMX and Django best practices

const htmxDjangoBestPractices = [
  "Use Django's template system with HTMX attributes",
  "Implement Django forms for form handling",
  "Utilize Django's URL routing system",
  "Use <PERSON><PERSON><PERSON>'s class-based views for HTMX responses",
  "Implement Django ORM for database operations",
  "Utilize Django's middleware for request/response processing",
];

// Folder structure

const folderStructure = `
project_name/
  app_name/
    templates/
    static/
      css/
      js/
    models.py
    views.py
    urls.py
  project_name/
    settings.py
    urls.py
manage.py
`;

// Additional instructions

const additionalInstructions = `
1. Use Django's template tags with HTMX attributes
2. Implement proper CSRF protection with Django's built-in features
3. Utilize Django's HttpResponse for HTMX-specific responses
4. Use <PERSON><PERSON><PERSON>'s form validation for HTMX requests
5. Implement proper error handling and logging
6. Follow Djan<PERSON>'s best practices for project structure
7. Use Djan<PERSON>'s staticfiles app for managing static assets
`;

