---
description: General rules for Next.js 14 development, including using the app directory, server components, and modern TypeScript syntax.
globs: **/app/**/*.*
---
- Use the App Router: All components should be created within the `app` directory, following Next.js 14 conventions.
- Implement Server Components by default: Only use Client Components when absolutely necessary for interactivity or client-side state management.
- Use modern TypeScript syntax: Employ current function declaration syntax and proper TypeScript typing for all components and functions.
- Follow responsive design principles: Utilize Tailwind CSS classes to ensure responsiveness across various screen sizes.
- Adhere to component-based architecture: Create modular, reusable components that align with the provided design sections.
- Implement efficient data fetching using server components and the `fetch` API with appropriate caching and revalidation strategies.
- Use Next.js 14's metadata API for SEO optimization.
- Employ Next.js Image component for optimized image loading.
- Ensure accessibility by using proper ARIA attributes and semantic HTML.
- Implement error handling using error boundaries and error.tsx files.
- Use loading.tsx files for managing loading states.
- Utilize route handlers (route.ts) for API routes in the App Router.
- Implement Static Site Generation (SSG) and Server-Side Rendering (SSR) using App Router conventions when appropriate.