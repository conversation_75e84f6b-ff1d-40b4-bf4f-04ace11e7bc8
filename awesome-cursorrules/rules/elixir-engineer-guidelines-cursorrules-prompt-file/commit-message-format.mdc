---
description: Applies commit message standards to all files in the project.
globs: **/*
---
- Use the following commit message format:
  <type>[optional scope]: <description>

  [optional body]

  [optional footer(s)]

  Where:

  type: One of the following: fix, feat, build, chore, ci, docs, perf, refactor, revert, style, test

  scope (optional): A noun describing a section of the codebase (e.g., fluxcd, deployment).

  description: A brief summary of the change in present tense.

  body (optional): A more detailed explanation of the change.

  footer (optional): One or more footers in the specified format.