# Elixir Engineer Guidelines .cursorrules prompt file

Author: <PERSON>

## What you can build
Elixir Microservices Platform: Develop a platform for creating and managing microservices with Elixir, leveraging Docker for containerization and PostgreSQL for data storage. Utilize Phoenix LiveView for real-time data updates and provide a dashboard for monitoring with Phoenix LiveDashboard.Real-Time Collaboration Tool: Create a web application using Elixir and Phoenix LiveView to allow multiple users to collaborate on projects simultaneously. Incorporate Tailwind CSS for modern, responsive styling, and use Ecto for managing project data in PostgreSQL.Automated DevOps Pipeline: Design a CI/CD tool that automates deployment processes using Docker, integrates with LeftHook for git hooks, and scans code with Sobelow and Credo for security and style issues. Utilize ExUnit for test automation and ExCoveralls for test coverage reports.Localisation Management System: Build a system for managing translations using Gettext, allowing users to easily add and update multilingual support for their projects. Integrate with a file system watcher to automatically reload changes and provide a user-friendly dashboard for managing texts.Secure Communication Platform: Develop a secure messaging application using Elixir and Phoenix, incorporating <PERSON><PERSON>osh for email sending, and <PERSON> for HTTP requests. Use Sobelow for continuous security scans and Plug for custom middleware integrations to ensure data security.Incident Monitoring and Response Tool: Create an alerting system using DNS Cluster for network monitoring and Phoenix LiveDashboard for visual insights. Utilize Ecto and PostgreSQL for logging incident data and Tailwind CSS for enhanced UI/UX design.Cloud-Based E-commerce Solution: Build a scalable e-commerce platform using Phoenix LiveView for dynamic product listings and PostgreSQL for transaction data management. Employ Docker for easy deployment and Swoosh for order confirmation emails.Interactive Learning Platform: Develop a platform for interactive coding tutorials utilizing Phoenix LiveView for real-time feedback and Ecto for exercise storage. Support multiple language translations with Gettext and ensure a seamless styling experience with Tailwind CSS.API Management and Gateway: Develop an API gateway solution using Elixir's Plug to route requests, allowing developers to set up API usage rules and monitor traffic with Phoenix LiveDashboard. Use Finch for outbound HTTP requests and Jason for data serialization.Customizable Dashboarding Tool: Create a tool for building customized dashboards using Phoenix LiveDashboard, allowing users to integrate their metrics via Ecto and visualize them with Tailwind CSS. Provide real-time data updates through Phoenix LiveView.Q1: How can we manage data consistency across distributed Elixir services using Ecto?Q2: What security best practices should be considered when building with Phoenix LiveView?Q3: In what ways can Docker enhance the scalability of an Elixir application?

## Benefits


## Synopsis
Developers working with Elixir and Phoenix would benefit by standardizing robust commit messages and building scalable, maintainable applications with comprehensive code quality and CI practices.

## Overview of .cursorrules prompt
The .cursorrules file outlines guidelines for an expert senior Elixir engineer working with a tech stack that includes Elixir, Phoenix, Docker, and various other tools and libraries. It emphasizes the importance of thorough consideration of code requirements before development and the provision of insightful follow-up questions after responses. The file also provides a structured approach to writing commit messages, detailing types, optional scope, description, and potential body or footer for changes made within software projects. This ensures clarity, consistency, and proper categorization of code alterations.

