# ES Module Node.js Guidelines .cursorrules prompt file

Author: <PERSON>

## What you can build
Agile Project Management Tool: A web-based application that helps teams plan, manage, and execute projects using agile methodologies. It can prioritize tasks, track progress, and provide analytics to optimize workflows.Modular Code Repository Platform: A platform akin to GitHub that encourages developers to create, share, and collaborate on modular and DRY components. Offers refactoring suggestions and promotes the usage of the latest ES and Node.js features.Performance and Security Testing Suite: A tool that automatically evaluates and suggests improvements for code performance and security. It identifies redundant code and potential security vulnerabilities during development.ES Module Converter Tool: A service that helps developers refactor legacy codebases into ES modules, ensuring they're using the latest JS features. Provides validation and error correction during the conversion process.Interactive Version-based Documentation Generator: A tool that generates concise to verbose documentation based on user-selected verbosity levels (V0 to V3). Explains code sections and highlights modular and agile practices used.AI-Powered Code Refactoring Suggestion Plugin: A code editor extension that suggests code improvements and refactorings as you type. Focuses on performance, modularity, and security enhancements.Task Prioritization AI Assistant: An AI tool that integrates with popular project management software to help teams break down tasks and prioritize them effectively, ensuring an agile workflow.ES and Node.js Learning Platform: An educational platform offering tutorials and interactive coding challenges to teach the latest ES and Node.js features, encouraging users to practice best coding practices.Comment Generator/Optimizer Tool: A plugin that helps developers create meaningful comments that describe code purpose, especially in sections where operations are non-obvious or uncommon libraries are used.Code History Comparator: A tool that compares different versions of a codebase, offering insights on improvements in performance, security, and modularity over time.

## Benefits


## Synopsis
Developers aiming for efficient, modular, and secure JavaScript applications using modern ES and Node.js features would benefit by building task-oriented, refactored, and well-commented code structures.

## Overview of .cursorrules prompt
The .cursorrules file outlines guidelines for software development practices, emphasizing modularity, performance, and security, while adhering to agile methodologies. It encourages breaking down tasks into prioritized steps and specifies response priorities based on verbosity levels (V0 to V3). For coding, it advises using ES module syntax, suggesting refactorings with the latest ES and Node.js features, and including TODO comments when necessary. Comments should clarify operations not obvious from the code and describe the purpose rather than the effect. The file emphasizes correcting errors without apologies.

