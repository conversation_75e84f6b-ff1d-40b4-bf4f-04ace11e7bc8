---
description: This rule focuses on code style, refactoring suggestions, and leveraging the latest ES and Node.js features for JavaScript, TypeScript, and Python files.
globs: *.js, *.jsx, *.ts, *.tsx, *.py
---
- Use ES module syntax
- Where appropriate suggest refactorings and code improvements
- Favor using the latest ES and nodejs features
- Don’t apologize for errors: fix them
  * If you can’t finish code, add TODO: comments