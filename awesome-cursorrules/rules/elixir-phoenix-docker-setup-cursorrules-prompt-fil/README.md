# Elixir Phoenix Docker Setup .cursorrules prompt file

Author: <PERSON>

## What you can build
WebSocket Monitoring Dashboard: Build a real-time dashboard for monitoring and visualizing active WebSocket connections and message traffics using Phoenix LiveView and Phoenix LiveDashboard.Multi-tenant SaaS Platform: Develop a multi-tenant SaaS application tailored for small businesses to manage customer interactions and project workflows, leveraging Ecto's capabilities for handling tenant data separation.AI-Powered Code Linter: Create a robust code linter for Elixir projects, integrating Credo and Sobelow analyses with AI-generated optimization suggestions.CI/CD Deployment Pipeline: Construct a CI/CD pipeline tool utilizing Docker, Release Please, and Phoenix with features for automated testing and deployment to k8s or other hosting environments.Localized Mailing List Manager: Implement an application for managing and sending localized emails using Swoosh and Gettext, with features for handling bulk distribution and tracking engagement.File System Event Tracker: Develop a service for monitoring file system changes and events, with real-time synchronization and notification capabilities using the File System Watcher.Cluster Management Tool: Create a DNS-based cluster management solution facilitating dynamic service discovery and load balancing in Elixir applications.Automated Test Coverage Analysis: Design a tool using ExCoveralls to generate comprehensive test coverage reports across multiple Elixir projects within a development ecosystem.JSON and API Explorer: Build a tool for testing and exploring JSON APIs and web services with comprehensive features powered by <PERSON> and <PERSON>lug.Elixir Learning Platform: Establish an online platform featuring interactive tutorials and exercises for mastering Elixir, Phoenix, and related technologies like Phoenix LiveView.Q1: How can Tailwind CSS be effectively integrated into an existing Phoenix application?Q2: What strategies can improve performance in Elixir applications when implemented with Docker?Q3: How should I approach securing a Phoenix application using Sobelow's analysis tool?

## Benefits


## Synopsis
Developers utilizing Elixir and Phoenix can leverage this prompt to improve their commit documentation and ensure comprehensive code review by incorporating robust conventional commit strategies and thought-provoking follow-up questions.

## Overview of .cursorrules prompt
The .cursorrules file defines guidelines and rules for an Elixir expert to follow when participating in a software development process using specific technologies such as Elixir, Phoenix, Docker, PostgreSQL, and more. It includes instructions for approaching code writing by considering all requirements before implementation. Additionally, it provides a structured format for conventional commit messages, detailing specific types, optional scopes, descriptions, body content, and footers to ensure clarity and consistency in code documentation and version control. The file also advises providing concise responses to inquiries prefixed with "VV" and suggests thought-provoking follow-up questions after providing solutions.

