---
description: Enforces code style and best practices for TypeScript files.
globs: **/*.ts
---
- Code should obey the rules defined in the .eslintrc.json, .prettierrc, and .editorconfig files.
- Lines should not be more than 80 characters.
- Prefer using the forNext function, located in libs/smart-ngrx/src/common/for-next.function.ts instead of for(let i;i < length;i++), forEach or for(x of y).
- Functions and methods should not have more than 4 parameters.
- Functions should not have more than 50 executable lines.