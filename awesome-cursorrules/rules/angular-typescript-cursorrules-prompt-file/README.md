# Angular TypeScript .cursorrules prompt file

Author: <PERSON>

## What you can build
Angular Code Refactoring Tool: A web application that allows developers to input existing Angular code and automatically refactor it according to modern development standards, ensuring compliance with ESLint, Prettier, HTMLHint, and Editorconfig rules.Angular Code Quality Checker: An online service that analyzes Angular code to check for compliance with coding standards, performance issues, readability, and maintainability, providing detailed reports and suggestions for improvement.Angular Best Practices Guide: A comprehensive web-based resource featuring up-to-date best practices for Angular development focusing on TypeScript, including examples and explanations designed to improve code readability, performance, and maintainability.Interactive Angular Workshop Platform: A platform offering interactive workshops and exercises for Angular developers to practice writing clean, readable code using TypeScript, with real-time feedback and code reviews by experts.Angular forNext Utilization Plugin: A plugin for popular IDEs that assists developers in converting traditional loops into the forNext function from libs/smart-ngrx/src/common/for-next.function.ts, improving consistency across projects.Angular Code Formatting Extension: An extension for VS Code that formats Angular code files by following the latest ESLint, <PERSON><PERSON><PERSON>, HTMLHint, and Editorconfig rules, ensuring a uniform code style.AI-Powered Angular Debugging Assistant: A service that integrates into IDEs to provide AI-driven debugging suggestions specifically for Angular projects, helping developers identify and resolve issues efficiently.Angular Performance Optimization Service: An online tool that analyzes Angular applications and suggests performance improvements, focusing on code efficiency, resource loading, and runtime performance enhancements.Angular Codebase Documentation Generator: A tool that automatically generates documentation for Angular projects, maintaining jsdoc comments and updating them to reflect the latest codebase changes, ensuring maintainability.Angular Component Library Analyzer: An application that evaluates custom Angular component libraries for adherence to best practices related to performance, readability, and maintainability, providing audit results and recommended changes.

## Benefits


## Synopsis
Angular developers aiming to enhance code quality and adhere to best practices can leverage this prompt to build clear, maintainable, and efficient Angular applications using the latest standards and testing with Jest.

## Overview of .cursorrules prompt
The .cursorrules file specifies guidelines for an expert Angular programmer using TypeScript, Angular 18, and Jest to produce code that is clear, readable, and performant. It emphasizes thoughtful and accurate reasoning, with a focus on providing well-reasoned answers. The file highlights best practices such as writing bug-free and fully functional code, ensuring proper imports and naming, and adhering to specific coding standards from configuration files like .eslintrc.json and .prettierrc. It also sets constraints on code structure and style, including limits on parameter count, lines of code, and nesting depth. The .cursorrules file encourages refactoring while preserving documentation and maintaining conciseness.

