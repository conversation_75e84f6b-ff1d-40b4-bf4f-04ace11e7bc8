---
description: Defines the visual aspects of the game and how the player observes the world. This includes map color-coding, screen effects, and the overall simulation style.
globs: visuals.py
---
- The map should be color-coded to show the owner of the square.
- There should be effects over the screen that mimic a CRT monitor.
- The game should aim to be similar to <PERSON>'s Game of Life, where the nations are the living organisms.
- Like <PERSON>'s Game of Life, nations should be able to "see" each other and react to each other.
- Like <PERSON>'s Game of Life, the nations should be able to "see" the resources and react to them.