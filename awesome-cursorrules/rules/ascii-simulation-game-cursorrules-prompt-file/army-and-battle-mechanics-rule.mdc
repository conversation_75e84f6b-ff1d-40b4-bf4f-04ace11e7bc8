---
description: Governs the mechanics related to armies, battles, and territorial control. This rule focuses on combat, resource management, and strategic expansion within the game.
globs: army_mechanics.py
---
- There is a "battle list" that shows all the battles that have happened and stats about them.
- Armies go from level 1 to level 10 based on their funding.
- Inner squares can be developed into farms, forests, mines.
- Armies require wood, food, and metal to be created.
- Nations must pay upkeep depending on the amount of armies and developed land they have.
- Battles are resolved by the difference in army level and a RISK-esque dice roll mechanic that is affected by army level.
- Armies can build castles that are good defensively and allow for funding of armies.
- Armies can be used to conquer squares from other nations.
- Armies can be used to defend squares from other nations.
- Armies can be used to attack other nations.
- Armies can be used to attack neutral squares.
- Armies can be used to attack other nations' squares.
- Armies can be used to attack neutral squares.
- Armies can be used to attack other nations' squares.
- Armies can be used to attack neutral squares.
- Nations should start with the same amount of gold and land.