---
description: Applies general best practices for HTMX, Go, and Fiber development to Go files. Focuses on Fiber framework usage.
globs: **/*.go
---
- Use Fiber's HTML rendering for server-side templates
- Implement Fiber's routing system for HTMX requests
- Utilize Fiber's middleware for request processing
- Use Fiber's JSON methods for API responses
- Implement proper error handling with Fiber's error handling
- Utilize Fiber's static file serving for assets