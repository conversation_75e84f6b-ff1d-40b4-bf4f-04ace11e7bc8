---
description: Applies general rules for Convex development, emphasizing schema design, validator usage, and correct handling of system fields.
globs: **/convex/**/*.*
---
- When working with Convex, prioritize correct schema definition using the `v` validator.
- Be aware of the automatically-generated system fields `_id` and `_creationTime`.
- See https://docs.convex.dev/database/types for available types.