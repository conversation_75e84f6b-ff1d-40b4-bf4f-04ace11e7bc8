# .cursorrules <PERSON>ursor AI WordPress Draft MacOS prompt file

Author: <PERSON>

## What you can build
WordPress Draft Manager: An enhanced management tool that extends the functionality of PressThat, allowing users to edit, publish, or delete draft posts directly from the system tray app without needing to log into their WordPress site.Multisite WordPress Integration: A version of PressThat designed for users managing multiple WordPress sites, providing the ability to switch between sites within the app and manage drafts from all sites in a single interface.PressThat Analytics: An extension to provide analytics on draft posts, including statistics on word count, estimated reading time, and draft aging alerts to inform users of how long drafts have been pending.Collaboration and Sharing Tool: A feature that lets users share drafts with team members directly from the app, allowing for collaboration and feedback before publication through comments and suggestions.Cross-Platform Synchronization: Develop an app that syncs draft data across multiple devices, ensuring that any drafts viewed or modified on one device are reflected across all devices connected to the user's account.Draft Reminder and Notification Service: Users can set reminders for working on drafts or receive notifications about drafts that haven't been updated for a set period.Customizable Draft Dashboard: A feature that allows users to customize the display of their draft post dashboard with different viewing modes, filters, and sorting options for better organization.AI Content Suggestions: An AI-powered feature that analyzes drafts and suggests improvements based on SEO best practices, readability, and engagement strategies.Offline Editing Mode: Allow users to continue working on drafts offline within the app, with changes syncing automatically once a connection is reestablished.Integration with Other Blogging Platforms: Expand the functionality to support draft management for other blogging platforms like Blogger, Medium, or Ghost, providing a unified dashboard for all blogging needs.

## Benefits


## Synopsis
Developers building WordPress management tools can utilize this prompt to create a desktop app for viewing and managing WordPress draft posts via the system tray.

## Overview of .cursorrules prompt
The .cursorrules file outlines a project named PressThat, a system tray application that interfaces with WordPress websites to manage draft posts. The app requires configuration with the user's WordPress website URL, username, and an Application Password obtained from the WordPress dashboard. The user experience involves downloading, installing, and opening the app, entering website credentials, testing the connection, and syncing draft posts. A menu bar or system tray icon displays the number of draft posts, and clicking the icon presents the main interface that combines cards and tables to showcase drafts, arranged by recency.

