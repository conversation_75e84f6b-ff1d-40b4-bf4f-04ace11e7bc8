---
description: Specifies testing guidelines for Flutter projects, covering unit, widget, and integration tests.
globs: test/**/*.*
---
- Write unit tests for business logic.
- Implement widget tests for UI components.
- Use integration tests for feature testing.
- Implement proper mocking strategies.
- Use proper test coverage tools.
- Follow proper test naming conventions.
- Implement proper CI/CD testing.