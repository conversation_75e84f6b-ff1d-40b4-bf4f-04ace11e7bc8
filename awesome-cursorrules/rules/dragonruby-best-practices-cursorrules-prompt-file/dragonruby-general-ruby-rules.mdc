---
description: Applies general Ruby coding style, structure, and best practices for DragonRuby projects.
globs: **/*.rb
---
- Write concise, idiomatic Ruby code with accurate examples.
- Follow Ruby and DragonRuby conventions and best practices.
- Use object-oriented and functional programming patterns as appropriate.
- Prefer iteration and modularization over code duplication.
- Structure files according to DragonRuby conventions.