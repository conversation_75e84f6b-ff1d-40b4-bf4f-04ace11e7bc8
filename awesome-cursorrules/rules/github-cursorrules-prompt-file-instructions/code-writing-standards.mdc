---
description: This rule enforces code writing standards relevant to different languages.
globs: **/*.*
---
- Follow Established Code-Writing Standards.
- Know your programming language's conventions in terms of spacing, comments, and naming.
- Consider extending some of these standards by creating internal coding rules for your organization. This can contain information on creating and naming folders or describing function names within your organization.