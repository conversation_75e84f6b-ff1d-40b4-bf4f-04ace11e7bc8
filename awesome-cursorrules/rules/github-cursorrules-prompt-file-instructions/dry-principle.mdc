---
description: This rule enforces the Don't Repeat Yourself principle to avoid code duplication and improve maintainability.
globs: **/*.*
---
- Follow the DRY (Don't Repeat Yourself) Principle and Avoid Duplicating Code or Logic.
- Avoid writing the same code more than once. Instead, reuse your code using functions, classes, modules, libraries, or other abstractions.
- Modify code in one place if you need to change or update it.