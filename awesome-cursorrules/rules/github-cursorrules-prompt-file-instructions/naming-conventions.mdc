---
description: This rule focuses on using meaningful and descriptive names for variables, functions, and classes throughout the project.
globs: **/*.*
---
- Choose names for variables, functions, and classes that reflect their purpose and behavior.
- A name should tell you why it exists, what it does, and how it is used. If a name requires a comment, then the name does not reveal its intent.
- Use specific names that provide a clearer understanding of what the variables represent and how they are used.