---
description: This rule enforces the single responsibility principle, ensuring functions are short and focused.
globs: **/*.*
---
- Write short functions that only do one thing.
- Follow the single responsibility principle (SRP), which means that a function should have one purpose and perform it effectively.
- If a function becomes too long or complex, consider breaking it into smaller, more manageable functions.