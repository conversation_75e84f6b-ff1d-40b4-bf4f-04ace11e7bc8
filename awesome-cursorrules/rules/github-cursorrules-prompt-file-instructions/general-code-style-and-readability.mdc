---
description: This rule applies to all files in the project and enforces general principles of clean code, readability, and maintainability.
globs: **/*.*
---
- Write code that is readable, understandable, and maintainable for future readers.
- Aim to create software that is not only functional but also readable, maintainable, and efficient throughout its lifecycle.
- Prioritize clarity to make reading, understanding, and modifying code easier.
- Adhere to established coding standards and write well-structured code to reduce errors.
- Regularly review and refactor code to improve structure, readability, and maintainability. Always leave the codebase cleaner than you found it.
- Use version control systems (e.g., Git) to track changes and collaborate effectively.