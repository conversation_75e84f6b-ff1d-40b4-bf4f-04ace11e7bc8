# Vue 3 前端开发技术规范（完整版）

## 1. 宗旨

本规范旨在为 Vue 3 项目提供一套统一的编码标准、最佳实践和风格指南。遵循此规范有助于：

- **提高代码一致性**：团队成员可以轻松理解和修改他人代码。
- **提升代码质量与可维护性**：结构清晰，易于调试、重构和扩展。
- **增强团队协作效率**：减少因风格不一造成的沟通成本和合并冲突。
- **沉淀最佳实践**：充分利用 Vue 3 的新特性，编写更高效、更现代的代码。

## 2. 技术栈规范

### 2.1. 核心技术栈

项目**必须**基于以下现代化技术栈构建：

- **Vue 3.x** - 使用组合式 API (Composition API)
- **TypeScript 5.x** - 强类型语言支持
- **Vite 5.x** - 新一代前端构建工具
- **Pinia 2.x** - Vue 官方推荐的状态管理库
- **Vue Router 4.x** - 官方路由管理器
- **Element Plus** - 企业级 UI 组件库
- **SCSS** - CSS 预处理器

### 2.2. 可视化与工具库

- **ECharts 5.x** - 数据可视化图表库
- **Axios** - HTTP 请求库
- **Day.js** - 轻量级日期处理库
- **VueUse** - Vue 组合式 API 工具集

### 2.3. 开发工具

- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Stylelint** - 样式代码检查
- **Husky** - Git hooks 工具
- **lint-staged** - Git 暂存文件检查
- **Commitlint** - Git 提交信息规范检查

## 3. 文件与目录结构

清晰的目录结构是项目可维护性的基础。

```
src/
├── api/             # API 请求模块 (按业务领域划分)
│   ├── modules/     # 业务模块 API
│   │   ├── user.ts
│   │   └── product.ts
│   ├── request.ts   # Axios 实例配置
│   └── types.ts     # API 相关类型定义
├── assets/          # 静态资源 (图片, 字体等，不区分业务)
│   ├── images/
│   ├── fonts/
│   └── icons/
├── components/      # 全局通用组件 (与业务无关)
│   ├── common/      # 基础原子组件 (Button.vue, Modal.vue, Icon.vue)
│   └── layout/      # 全局布局组件 (Header.vue, Sidebar.vue, PageWrapper.vue)
├── composables/     # 全局通用组合式函数 (useRequest.ts, useTheme.ts)
├── config/          # 全局配置
│   ├── index.ts     # 环境变量配置
│   └── chart.ts     # ECharts 全局配置
├── directives/      # 全局自定义指令
├── layouts/         # 页面布局 (一级路由 <router-view> 的容器)
│   ├── DefaultLayout.vue
│   └── BlankLayout.vue
├── plugins/         # 第三方插件配置
│   ├── element-plus.ts
│   └── echarts.ts
├── router/          # 路由配置
│   ├── index.ts
│   ├── routes.ts
│   └── guards.ts    # 路由守卫
├── store/           # Pinia 状态管理 (包含通用 store 和业务 store)
│   ├── modules/
│   │   ├── user.ts      # 用户业务 store
│   │   └── product.ts   # 商品业务 store
│   ├── app.ts           # 全局应用 store (如主题, 语言)
│   └── index.ts         # Store 入口
├── styles/          # 全局样式与变量
│   ├── variables.scss   # SCSS 变量
│   ├── mixins.scss      # SCSS 混入
│   ├── reset.scss       # 样式重置
│   └── index.scss       # 样式入口
├── types/           # 全局 TypeScript 类型定义
│   ├── global.d.ts
│   ├── components.d.ts  # 组件类型声明
│   └── env.d.ts         # 环境变量类型
├── utils/           # 全局通用工具函数 (request.ts, storage.ts)
│   ├── request.ts       # HTTP 请求封装
│   ├── storage.ts       # 本地存储封装
│   ├── validate.ts      # 表单验证工具
│   └── format.ts        # 数据格式化工具
└── views/           # 业务模块目录 (核心)
    ├── user/          # "用户"业务模块
    │   ├── components/  # 只在"用户"模块内部使用的组件 (UserForm.vue, UserTable.vue)
    │   ├── composables/ # 只在"用户"模块内部使用的 Hooks (useUserSearch.ts)
    │   ├── types.ts     # 只在"用户"模块内部使用的类型定义
    │   ├── UserList.vue # 模块页面
    │   └── UserDetail.vue # 模块页面
    ├── product/       # "商品"业务模块
    │   ├── components/  # (ProductCard.vue, ProductFilter.vue)
    │   └── ProductList.vue
    ├── dashboard/     # 数据看板模块
    │   ├── components/
    │   │   ├── ChartCard.vue
    │   │   └── StatisticCard.vue
    │   └── DashboardView.vue
    └── home/
        └── HomePage.vue
├── App.vue          # 根组件
└── main.ts          # 入口文件
```

## 4. 命名规范

- **组件文件与组件名**：**必须** 使用 `PascalCase` (大驼峰命名法)。
- **非组件的 `.vue` 文件** (如 `App.vue`, 布局文件): **必须** 使用 `PascalCase`。
- **其他文件** (`.ts`, `.js`, `.scss`等): **必须** 使用 `kebab-case` (短横线命名法)。
- **变量与函数名**：**必须** 使用 `camelCase` (小驼峰命名法)。
- **常量**：**必须** 使用 `UPPER_CASE_WITH_UNDERSCORES` (全大写下划线)。
- **`ref` 变量**：**推荐** 在简单场景下无需特殊后缀，但在复杂逻辑或与 `reactive` 状态区分时，可加上 `Ref` 后缀。
- **`reactive` 变量**：**推荐** 以 `State` 结尾，表示其为响应式状态对象。
- **类型/接口命名**：**必须** 使用 `PascalCase`，接口名**推荐**以 `I` 开头或使用 `type` 关键字。
- **枚举命名**：**必须** 使用 `PascalCase`，枚举成员使用 `UPPER_CASE`。

## 5. 组件设计与规范

组件是 Vue 应用的基石。遵循一致的规范可以极大地提升组件的可复用性、可维护性和健壮性。

### 5.1. 组件命名规范 (Naming Conventions)

统一的命名是项目清晰度的第一步。

- **文件名 (PascalCase)**: 组件文件**必须**使用大驼峰命名法（PascalCase）。
- **基础组件前缀 (Base/App)**: 全局通用的、与业务无关的基础组件**推荐**使用 `Base` 或 `App` 作为前缀。这使得它们易于识别、全局注册和通过IDE自动导入。
- **单例组件前缀 (The)**: 在任何指定页面或布局中只会出现一次的组件，**推荐**使用 `The` 作为前缀，以表示其单例性质。
- **业务组件命名**: 与特定业务紧密相关的组件，其命名应直接体现其业务功能，并放置在相应的业务模块目录下（见项目结构规范）。

### 5.2. 组件设计模式与原则

- **单一职责原则 (SRP)**: 一个组件只做好一件事。如果组件逻辑复杂、模板冗长（例如超过200行），就应考虑将其拆分为更小、更专注的子组件。
- **Props Down, Events Up**: 数据**必须**通过 `props` 从父组件单向流向子组件。子组件**严禁**直接修改 `props`。子组件应通过 `emits` 将事件和数据变更通知父组件，由父组件来执行状态更新。
- **优先使用"展示组件"与"容器组件"模式**:

  - **展示组件 (Presentational/Dumb Components)**:

    - **职责**: 只关心"长什么样"。
    - **特征**: 通过 `props` 接收数据，通过 `emits` 发出事件。内部通常不包含复杂的业务逻辑、不直接请求API、不直接与Pinia Store交互。
    - **优点**: 高度可复用、易于测试和维护。
    - **位置**: `src/components/common/`
  - **容器组件 (Container/Smart Components)**:

    - **职责**: 只关心"如何工作"。
    - **特征**: 负责获取数据（API请求）、管理复杂状态（连接Pinia Store）、处理业务逻辑，然后将处理好的数据作为 `props` 传递给展示组件。
    - **优点**: 将业务逻辑与UI渲染分离，使代码结构更清晰。
    - **位置**: `src/views/` 或 `views/xxx/components/` (作为业务模块的组成部分)

### 5.3. 组件 API 设计 (Props, Emits, Slots, Expose)

组件的 API 定义了它如何与外部世界交互，必须做到清晰、明确、健壮。

#### 5.3.1. Props

- **必须** 使用 `<script setup>` 内的 `defineProps`。
- **必须** 为每个 `prop` 提供明确的 `type`。对于复杂类型，使用 `as PropType<T>` 提供精确的TypeScript类型。
- **推荐** 为非 `required: true` 的 `prop` 提供有意义的 `default` 值。
- **布尔类型 Props**: 其命名应像一个状态（如 `loading`, `disabled`），且 `default` 值**必须**为 `false`。在模板中使用时，仅写属性名即代表 `true` (`<MyComponent disabled />`)。
- **谨慎使用 `validator`**: 仅在类型无法满足校验需求时使用 `validator` 函数进行更复杂的校验。
#### 5.3.2. Emits

- **必须** 使用 `<script setup>` 内的 `defineEmits` 来显式声明所有会触发的事件。
- **强烈推荐** 使用 TypeScript 的泛型语法来定义 `emits`，以获得完整的类型推断和载荷校验。
- 事件名**必须**使用 `kebab-case`（短横线分隔命名）。
- 对于 `v-model`，**必须** 使用 `update:propName` 的标准事件名。

```typescript
// 基础事件定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string, oldValue: string]
  'submit': [data: FormData]
  'error': [error: Error]
}>()

// 触发事件
const handleChange = (newValue: string) => {
  emit('update:modelValue', newValue)
  emit('change', newValue, oldValue)
}
```

#### 5.3.3. Slots

- **用途**: 当你需要向组件传递模板片段（HTML结构）而不是数据时，使用 `slots`。
- **默认插槽**: 为最常见的内容提供一个匿名的 `<slot />`。
- **具名插槽**: 当组件需要多个可定制区域时，使用具名插槽（如 `#header`, `#footer`）。
- **作用域插槽**: 当你需要将子组件内部的数据暴露给父组件的插槽模板时，使用作用域插槽。

#### 5.3.4. `defineExpose`

- **用途**: 允许父组件通过 `ref` 访问子组件内部的特定属性或方法。
- **使用原则**: **谨慎使用**！`defineExpose` 会破坏组件的封装性。它应被视为一种"逃生舱口"，而不是常规的通信方式。
- **适用场景**: 主要用于需要命令式调用的场景，如表单的 `reset` 方法、视频播放器的 `play/pause` 方法等。

## 6. `script setup` 与组合式 API 规范

- **必须** 使用 `<script setup lang="ts">` 语法，它是 Vue 3 的标准和未来。
- **代码组织顺序**：**推荐** 遵循以下顺序，以保持逻辑清晰。

  1. `import` 语句（第三方库 → Vue 相关 → 项目内部）
  2. `defineProps`, `defineEmits`, `defineExpose` 等宏
  3. `useRouter`, `useRoute`, `useStore` 等 Hooks 调用
  4. 响应式状态 (`ref`, `reactive`)
  5. `computed` 属性
  6. `watch` 和 `watchEffect`
  7. 生命周期钩子 (按执行顺序排列: `onMounted`, `onUpdated`, `onUnmounted` 等)
  8. 普通函数 (事件处理、业务逻辑等)
- **`ref` vs `reactive`**

  - **推荐** `ref` 用于基本类型 (`string`, `number`, `boolean`) 和需要重新赋值的引用类型 (如数组)。
  - **推荐** `reactive` 用于不需要重新赋值的复杂对象 (如表单状态)。
  - 访问 `ref` 值时，在 `<script>` 中使用 `.value`，在 `<template>` 中则不需要。
- **`computed`**：用于派生状态。**必须** 是纯函数，不应有副作用。
- **`watch`**：用于侦听数据变化并执行副作用 (如 API 请求)。明确指定侦听源，避免模糊侦听。

## 7. 模板 (Template) 规范

- **指令顺序**：**推荐** 遵循统一的属性/指令顺序，可由 ESLint 规则自动格式化。

  1. `v-if`, `v-else-if`, `v-else`
  2. `v-for`
  3. `v-model`
  4. `ref`, `key`
  5. 属性 (静态和动态)
  6. `v-on` 事件监听
- **`v-for` 必须有 `key`**：`key` **必须** 是唯一且稳定的值，如 `item.id`。**禁止** 使用 `index` 作为 `key`，除非列表是纯静态且无顺序变化。
- **避免 `v-if` 和 `v-for` 在同一元素上**：这会导致性能问题。应将 `v-if` 移到外层容器或使用 `computed` 属性预处理列表。
- **组件自闭合**：没有子内容的组件**推荐**使用自闭合标签。
- **Element Plus 组件使用**：使用 `el-` 前缀的组件时，**推荐**按需引入，避免全局注册所有组件。

## 8. 样式 (CSS/SCSS) 规范

- **必须使用 `<style scoped lang="scss">`**：确保组件样式不会污染全局作用域。
- **SCSS 变量管理**：全局变量（如颜色、字体、间距）**必须**定义在 `src/styles/variables.scss` 中。
- **类名命名**：**推荐** 使用 `BEM` (Block Element Modifier) 或 `kebab-case` 结合组件名作为前缀，避免与子组件样式冲突。
- **`:deep()` 深度选择器**：仅在需要修改子组件内部样式时谨慎使用，避免破坏组件封装性。
- **CSS 变量**：对于主题色、字体大小等全局样式，**推荐** 在根作用域定义 CSS 变量，便于动态切换主题。
- **响应式设计**：**推荐**使用 SCSS mixin 定义响应式断点。

## 9. TypeScript 使用规范

- **全面拥抱 TypeScript**：所有 `.js` 文件都应替换为 `.ts` 文件。
- **定义清晰的类型**：为 API 响应、Pinia `state`、函数参数和返回值等提供精确的类型定义。将通用类型放在 `src/types` 目录下。
- **利用 `PropType`**：为复杂 `props` 提供类型支持。
- **避免使用 `any`**：`any` 会使 TypeScript 失去类型检查的意义。优先使用 `unknown` 或更具体的类型。
- **类型导入**：**推荐**使用 `import type` 导入纯类型，以便打包工具进行优化。

## 10. 状态管理 (Pinia) 规范

- **模块化**：按业务模块在 `src/store/modules/` 下创建独立的 store。
- **结构清晰**：每个 store **必须** 包含 `state`、`getters` 和 `actions`。
  - `state`: **必须** 是一个函数，返回初始状态对象。
  - `getters`: 类似于 `computed`，用于派生状态。
  - `actions`: 用于处理异步操作和修改 `state` 的业务逻辑。**禁止** 在组件中直接修改 `state`，**必须** 通过 `actions`。
- **持久化**：对于需要持久化的状态（如用户信息、主题设置），**推荐**使用 `pinia-plugin-persistedstate`。
- **类型安全**：**必须**为 store 的 state、getters 和 actions 提供完整的类型定义。

## 11. 路由 (Vue Router) 规范

- **懒加载**：所有页面级路由**必须**使用动态 `import()` 进行懒加载，以优化首屏加载速度。
- **路由命名**：**推荐** 为每个路由设置唯一的 `name`，方便编程式导航和缓存。
- **元信息 (meta)**：使用 `meta` 字段存储路由相关的元信息，如页面标题、权限要求、是否需要缓存 (`keep-alive`) 等。
- **路由守卫**：将全局守卫逻辑抽离到 `router/guards.ts` 中，保持路由配置文件的简洁。
- **路由分组**：按照业务模块对路由进行分组管理。

## 12. API 请求规范

- **统一封装 Axios**：在 `src/api/request.ts` 中统一配置 Axios 实例。
- **请求拦截器**：统一添加 token、设置超时时间等。
- **响应拦截器**：统一处理错误、token 过期等情况。
- **按模块组织 API**：将 API 请求按业务模块组织在 `src/api/modules/` 下。
- **类型定义**：为所有 API 请求和响应定义明确的 TypeScript 类型。

## 13. ECharts 使用规范

- **按需引入**：只引入需要的图表类型和组件，减少打包体积。
- **全局配置**：在 `src/config/chart.ts` 中定义全局主题和通用配置。
- **封装图表组件**：将常用图表封装为可复用组件。
- **响应式适配**：图表**必须**监听容器大小变化并自动调整。

## 14. 组合式函数 (Composables) 规范

- **命名规范**：**必须**以 `use` 开头，使用 `camelCase` 命名。
- **单一职责**：一个 composable 只负责一个功能。
- **返回值**：**推荐**返回对象，便于解构和按需使用。
- **响应式**：确保返回的状态是响应式的。

## 15. Element Plus 使用规范

- **按需引入**：**推荐**使用自动导入插件，避免手动引入所有组件。
- **全局配置**：在 `src/plugins/element-plus.ts` 中进行全局配置。
- **主题定制**：通过 SCSS 变量覆盖 Element Plus 的默认主题。
- **国际化**：配置中文语言包。

## 16. 代码格式化与质量保证

### 16.1. ESLint 配置

```javascript
// .eslintrc.cjs
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'eslint:recommended',
    '@vue/eslint-config-typescript/recommended',
    '@vue/eslint-config-prettier'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // Vue 规则
    'vue/multi-word-component-names': 'off',
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/component-options-name-casing': ['error', 'PascalCase'],
    'vue/custom-event-name-casing': ['error', 'kebab-case'],
    'vue/no-v-html': 'off',
    'vue/require-default-prop': 'off',
    'vue/require-explicit-emits': 'error',
    'vue/html-self-closing': ['error', {
      html: {
        void: 'always',
        normal: 'never',
        component: 'always'
      }
    }],
  
    // TypeScript 规则
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': ['error', {
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    '@typescript-eslint/explicit-module-boundary-types': 'off',
  
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'prefer-const': 'error',
    'no-var': 'error'
  }
};
```

### 16.2. Prettier 配置

```javascript
// .prettierrc.cjs
module.exports = {
  semi: true,
  singleQuote: true,
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  trailingComma: 'none',
  endOfLine: 'lf',
  arrowParens: 'always',
  bracketSpacing: true,
  htmlWhitespaceSensitivity: 'ignore',
  vueIndentScriptAndStyle: false
};
```

### 16.3. Stylelint 配置

```javascript
// .stylelintrc.cjs
module.exports = {
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-config-recommended-vue/scss',
    'stylelint-config-recess-order'
  ],
  rules: {
    'selector-class-pattern': null,
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['deep', 'global']
      }
    ],
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['v-deep', 'v-global', 'v-slotted']
      }
    ],
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: ['function', 'if', 'each', 'include', 'mixin', 'use']
      }
    ]
  }
};

### 16.4. Git Hooks 配置

```javascript
// .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
```

```json
// package.json
{
  "scripts": {
    "prepare": "husky install",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore",
    "lint:style": "stylelint \"src/**/*.{css,scss,vue}\" --fix",
    "format": "prettier --write ."
  },
  "lint-staged": {
    "*.{vue,js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{css,scss,vue}": [
      "stylelint --fix",
      "prettier --write"
    ],
    "*.{json,md,html}": [
      "prettier --write"
    ]
  }
}
```

### 16.5. Commitlint 配置

```javascript
// commitlint.config.cjs
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复 bug
        'docs',     // 文档更新
        'style',    // 代码格式（不影响代码运行的变动）
        'refactor', // 重构
        'perf',     // 性能优化
        'test',     // 增加测试
        'chore',    // 构建过程或辅助工具的变动
        'revert',   // 回退
        'build'     // 打包
      ]
    ],
    'subject-case': [0]
  }
};
```

```javascript
// .husky/commit-msg
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx --no -- commitlint --edit ${1}
```

## 17. 注释规范

### 17.1. 文件注释

```typescript
/**
 * @description 用户管理相关 API
 * <AUTHOR>
 * @date 2024-01-01
 */
```

### 17.2. 组件注释

```vue
<script setup lang="ts">
/**
 * @component UserTable
 * @description 用户列表表格组件
 * 
 * @props
 * - data: 表格数据
 * - loading: 加载状态
 * 
 * @emits
 * - edit: 编辑用户
 * - delete: 删除用户
 * 
 * @example
 * <UserTable
 *   :data="userList"
 *   :loading="loading"
 *   @edit="handleEdit"
 *   @delete="handleDelete"
 * />
 */
</script>
```

### 17.3. 函数注释

```typescript
/**
 * 格式化日期
 * @param date - 日期对象或时间戳
 * @param format - 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 * @example
 * formatDate(new Date(), 'YYYY-MM-DD') // '2024-01-01'
 */
export function formatDate(
  date: Date | number,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  // 实现代码
}
```

### 17.4. 复杂逻辑注释

```typescript
// Good: 解释"为什么"
// 使用防抖处理搜索请求，避免频繁请求导致服务器压力过大
const debouncedSearch = useDebounceFn(search, 500);

// Bad: 只描述"做了什么"
// 创建一个防抖函数
const debouncedSearch = useDebounceFn(search, 500);
```

### 17.5. TODO 注释

```typescript
// TODO: [旋风] 2024-01-01 - 需要优化查询性能
// FIXME: [旋风] 2024-01-01 - 修复在 Safari 中的样式问题
// HACK: [旋风] 2024-01-01 - 临时方案，等待后端接口更新
// NOTE: [旋风] 2024-01-01 - 这里的逻辑比较复杂，需要注意边界情况
```

## 18. 性能优化规范

### 18.1. 组件优化

- **使用 `v-show` vs `v-if`**：频繁切换使用 `v-show`，条件很少改变使用 `v-if`。
- **使用 `keep-alive`**：缓存不活动的组件实例。
- **组件懒加载**：大型组件使用异步组件。

```vue
<template>
  <!-- 使用 keep-alive 缓存页面 -->
  <router-view v-slot="{ Component }">
    <keep-alive :include="cachedViews">
      <component :is="Component" :key="$route.fullPath" />
    </keep-alive>
  </router-view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 根据路由 meta 决定是否缓存
const cachedViews = computed(() => {
  return route.meta.keepAlive ? [route.name as string] : [];
});
</script>
```

### 18.2. 列表优化

- **虚拟滚动**：大列表使用虚拟滚动。
- **分页**：合理使用分页，避免一次性加载大量数据。

```vue
<template>
  <!-- 使用虚拟滚动库，如 vue-virtual-scroller -->
  <RecycleScroller
    :items="items"
    :item-size="50"
    key-field="id"
  >
    <template #default="{ item }">
      <div class="item">{{ item.name }}</div>
    </template>
  </RecycleScroller>
</template>
```

### 18.3. 图片优化

- **懒加载**：使用 `v-lazy` 指令或 Intersection Observer API。
- **响应式图片**：根据设备提供不同尺寸的图片。
- **WebP 格式**：优先使用 WebP 格式，提供降级方案。

```vue
<template>
  <!-- 图片懒加载 -->
  <img v-lazy="imageSrc" alt="描述" />
  
  <!-- 响应式图片 -->
  <picture>
    <source srcset="image.webp" type="image/webp" />
    <source srcset="image.jpg" type="image/jpeg" />
    <img src="image.jpg" alt="描述" />
  </picture>
</template>
```

### 18.4. 打包优化

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    vue(),
    // 打包体积分析
    visualizer({
      open: true,
      gzipSize: true,
      brotliSize: true
    })
  ],
  build: {
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 Vue 相关库打包到一起
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // 将 Element Plus 单独打包
          'element-plus': ['element-plus'],
          // 将 ECharts 单独打包
          'echarts': ['echarts']
        }
      }
    },
    // 启用 CSS 代码分割
    cssCodeSplit: true,
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000
  }
});
```

## 19. 测试规范

### 19.1. 单元测试

- **测试框架**：使用 Vitest + Vue Test Utils
- **测试覆盖率**：**推荐** 核心业务逻辑和公共组件达到 80% 以上覆盖率
- **测试文件命名**：与被测试文件同名，添加 `.test.ts` 或 `.spec.ts` 后缀

```typescript
// utils/format.test.ts
import { describe, it, expect } from 'vitest';
import { formatDate, formatNumber } from './format';

describe('format utils', () => {
  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = new Date('2024-01-01 12:30:45');
      expect(formatDate(date, 'YYYY-MM-DD')).toBe('2024-01-01');
      expect(formatDate(date, 'YYYY-MM-DD HH:mm:ss')).toBe('2024-01-01 12:30:45');
    });

    it('should handle timestamp', () => {
      const timestamp = 1704096645000; // 2024-01-01 12:30:45
      expect(formatDate(timestamp, 'YYYY-MM-DD')).toBe('2024-01-01');
    });
  });

  describe('formatNumber', () => {
    it('should format number with thousand separator', () => {
      expect(formatNumber(1234567)).toBe('1,234,567');
      expect(formatNumber(1234567.89, 2)).toBe('1,234,567.89');
    });
  });
});
```

### 19.2. 组件测试

```typescript
// components/common/BaseButton.test.ts
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import BaseButton from './BaseButton.vue';

describe('BaseButton', () => {
  it('renders properly', () => {
    const wrapper = mount(BaseButton, {
      props: { text: 'Click me' }
    });
    expect(wrapper.text()).toContain('Click me');
  });

  it('emits click event', async () => {
    const wrapper = mount(BaseButton);
    await wrapper.trigger('click');
    expect(wrapper.emitted()).toHaveProperty('click');
  });

  it('is disabled when loading', () => {
    const wrapper = mount(BaseButton, {
      props: { loading: true }
    });
    expect(wrapper.attributes('disabled')).toBeDefined();
  });
});
```

## 20. 环境变量与配置

### 20.1. 环境变量文件

```bash
# .env.development
VITE_APP_TITLE=系统名称（开发环境）
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_ENV=development

# .env.production
VITE_APP_TITLE=系统名称
VITE_API_BASE_URL=https://api.example.com
VITE_APP_ENV=production

# .env.staging
VITE_APP_TITLE=系统名称（测试环境）
VITE_API_BASE_URL=https://api-test.example.com
VITE_APP_ENV=staging
```

### 20.2. 类型声明

```typescript
// types/env.d.ts
/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_APP_ENV: 'development' | 'production' | 'staging';
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
```

### 20.3. 配置使用

```typescript
// config/index.ts
export const config = {
  title: import.meta.env.VITE_APP_TITLE,
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  env: import.meta.env.VITE_APP_ENV,
  isDev: import.meta.env.DEV,
  isProd: import.meta.env.PROD
};
```

## 21. 错误处理规范

### 21.1. 全局错误处理

```typescript
// main.ts
import { createApp } from 'vue';
import { ElMessage } from 'element-plus';
import App from './App.vue';

const app = createApp(App);

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err);
  console.error('Component:', instance);
  console.error('Error info:', info);
  
  // 显示错误提示
  ElMessage.error('系统错误，请稍后重试');
  
  // 可以将错误上报到监控平台
  // reportError(err, instance, info);
};

// 全局警告处理（仅开发环境）
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('Warning:', msg);
    console.warn('Component:', instance);
    console.warn('Trace:', trace);
  };
}

app.mount('#app');
```

### 21.2. 组件错误边界

```vue
<!-- components/common/ErrorBoundary.vue -->
<template>
  <div v-if="error" class="error-boundary">
    <div class="error-content">
      <el-icon :size="64" color="#F56C6C">
        <WarningFilled />
      </el-icon>
      <h3>出错了</h3>
      <p>{{ error.message }}</p>
      <el-button @click="handleReset">重新加载</el-button>
    </div>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue';
import { WarningFilled } from '@element-plus/icons-vue';

const error = ref<Error | null>(null);

onErrorCaptured((err: Error) => {
  error.value = err;
  console.error('Component error captured:', err);
  return false; // 阻止错误继续向上传播
});

function handleReset() {
  error.value = null;
}
</script>

<style lang="scss" scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;

  .error-content {
    text-align: center;

    h3 {
      margin: 16px 0 8px;
      font-size: 20px;
      color: #303133;
    }

    p {
      margin: 0 0 24px;
      color: #909399;
    }
  }
}
</style>
```

## 22. 安全规范

### 22.1. XSS 防护

- **避免使用 `v-html`**：除非内容已经过严格清理。
- **使用 DOMPurify**：清理不可信的 HTML 内容。

```typescript
// utils/security.ts
import DOMPurify from 'dompurify';

/**
 * 清理 HTML 内容，防止 XSS 攻击
 */
export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
    ALLOWED_ATTR: ['href', 'title', 'target']
  });
}
```

### 22.2. CSRF 防护

```typescript
// api/request.ts
// 在请求拦截器中添加 CSRF Token
this.instance.interceptors.request.use((config) => {
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  if (csrfToken) {
    config.headers['X-CSRF-TOKEN'] = csrfToken;
  }
  return config;
});
```

### 22.3. 敏感信息保护

- **不在前端存储敏感信息**：如密码、完整的银行卡号等。
- **Token 存储**：使用 `httpOnly` Cookie 或加密存储到 localStorage。
- **HTTPS**：生产环境**必须**使用 HTTPS。

## 23. 可访问性规范

- **语义化 HTML**：使用正确的 HTML 标签。
- **ARIA 属性**：为屏幕阅读器提供额外信息。
- **键盘导航**：确保所有交互元素可通过键盘访问。
- **颜色对比度**：确保文字与背景有足够的对比度（WCAG AA 标准）。

```vue
<template>
  <!-- 语义化标签 -->
  <nav aria-label="主导航">
    <ul>
      <li><a href="/">首页</a></li>
      <li><a href="/about">关于</a></li>
    </ul>
  </nav>

  <!-- ARIA 属性 -->
  <button
    aria-label="关闭对话框"
    aria-expanded="true"
    @click="handleClose"
  >
    <el-icon><Close /></el-icon>
  </button>

  <!-- 键盘导航 -->
  <div
    role="button"
    tabindex="0"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space.prevent="handleClick"
  >
    点击我
  </div>
</template>
```

## 24. 国际化规范

```typescript
// locales/zh-CN.ts
export default {
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    search: '搜索',
    reset: '重置',
    submit: '提交',
    loading: '加载中...',
    noData: '暂无数据'
  },
  user: {
    title: '用户管理',
    username: '用户名',
    email: '邮箱',
    role: '角色',
    status: '状态',
    createTime: '创建时间'
  }
};

// locales/en-US.ts
export default {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    search: 'Search',
    reset: 'Reset',
    submit: 'Submit',
    loading: 'Loading...',
    noData: 'No Data'
  },
  user: {
    title: 'User Management',
    username: 'Username',
    email: 'Email',
    role: 'Role',
    status: 'Status',
    createTime: 'Create Time'
  }
};

// 使用
<template>
  <h1>{{ $t('user.title') }}</h1>
  <el-button>{{ $t('common.save') }}</el-button>
</template>
```

## 25. 文档规范

### 25.1. README.md

项目根目录**必须**包含 `README.md`，内容包括：

- 项目简介
- 技术栈
- 目录结构
- 开发环境要求
- 安装步骤
- 开发命令
- 构建部署
- 贡献指南

### 25.2. 组件文档

复杂组件**推荐**编写独立的文档，说明：

- 组件功能
- Props 说明
- Events 说明
- Slots 说明
- 使用示例

## 26. 总结

本规范涵盖了 Vue 3 + TypeScript + Element Plus + Vite 技术栈的完整开发规范，包括：

1. **项目结构**：清晰的目录组织
2. **代码规范**：统一的命名和编码风格
3. **组件设计**：可复用、可维护的组件架构
4. **状态管理**：Pinia 最佳实践
5. **路由管理**：懒加载和权限控制
6. **API 请求**：统一的请求封装
7. **可视化**：ECharts 使用规范
8. **性能优化**：加载、渲染和打包优化
9. **代码质量**：ESLint、Prettier、测试
10. **安全规范**：XSS、CSRF 防护
11. **可访问性**：无障碍设计
12. **国际化**：多语言支持

遵循本规范可以帮助团队构建高质量、可维护、可扩展的 Vue 3 应用程序。
